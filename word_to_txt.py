#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Word文档转文本文件工具
将Word文档(.docx)转换为纯文本文件(.txt)
"""

import os
import sys
from docx import Document


def word_to_txt(docx_path, txt_path=None):
    """
    将Word文档转换为文本文件
    
    Args:
        docx_path (str): Word文档路径
        txt_path (str, optional): 输出文本文件路径，如果不指定则自动生成
    
    Returns:
        str: 输出文件路径
    """
    # 检查输入文件是否存在
    if not os.path.exists(docx_path):
        raise FileNotFoundError(f"找不到文件: {docx_path}")
    
    # 如果没有指定输出路径，则自动生成
    if txt_path is None:
        base_name = os.path.splitext(docx_path)[0]
        txt_path = f"{base_name}.txt"
    
    try:
        # 读取Word文档
        print(f"正在读取Word文档: {docx_path}")
        doc = Document(docx_path)
        
        # 提取所有段落的文本
        text_content = []
        for paragraph in doc.paragraphs:
            text_content.append(paragraph.text)
        
        # 将文本写入文件
        print(f"正在写入文本文件: {txt_path}")
        with open(txt_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(text_content))
        
        print(f"转换完成！输出文件: {txt_path}")
        return txt_path
        
    except Exception as e:
        print(f"转换过程中出现错误: {str(e)}")
        raise


def main():
    """主函数"""
    # 转换03.docx为03.txt
    input_file = "03.docx"
    output_file = "权倾天下：废王的逆袭/原文03.txt"
    
    try:
        result_path = word_to_txt(input_file, output_file)
        print(f"\n✅ 成功将 {input_file} 转换为 {result_path}")
        
        # 显示文件大小信息
        input_size = os.path.getsize(input_file)
        output_size = os.path.getsize(result_path)
        print(f"📊 原文件大小: {input_size:,} 字节")
        print(f"📊 输出文件大小: {output_size:,} 字节")
        
    except Exception as e:
        print(f"❌ 转换失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
